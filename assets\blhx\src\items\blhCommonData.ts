/**
 * 通用数据,用于存档
 * 
 */

export interface OwnedHero {
    instanceId: string; // 唯一ID
    roleId: number;//角色id
    level: number;//等级
    star: number;//星级
    fightNum: number;//战力
    fightState: number;//状态
    camp: number;//阵营
    career: number;//职业
    atk: number;//攻击力
}

import blhkc from "../utils/blhkc";


export default class blhCommonData {

    uid: string = 'bData';
    /**道具数量 */
    propData = {
    }
    /** 建造界面数据*/
    buildData = {
        "watchAd": 0, //许愿次数
        "freeCnt": 0, //免费次数
        "rewardAddCnt": 0 //积分数值
    }
    /**建造角色相关数据 */
    buildRoleData = {
        pieceNumList:{},//角色数量
        lv: {},//等级
        fightState: {},
    }

    manyRoleData:any = [];


    /** 读档 */
    init() {
        const saved = blhkc.getData(this.uid, {});
        for (const key in saved) {
            if (this[key] !== undefined) {
                this[key] = saved[key];
            }
        }
        // if (!blhkc.getData(saveData.regTime, 0)) {
        //     blhkc.saveData(saveData.regTime, Date.now())
        // }
        // if (blhkc.isNewDay(blhkc.getData(saveData.todayloginTime, 0))) {
        //     blhkc.saveData(saveData.todayloginTime, Date.now());
        // }
        // this.level = blhkc.getData(saveData.curRank, 0);
        // this.level_llk = blhkc.getData(saveData.curLLKRank, 0);
        // if (this.stayTime > 0) {
        //     this.stayDay = blhkc.calculateDays(this.stayTime, Date.now());
        // }
    }

    resetData() {
        // if(this.vit < 30){
        //     this.vit = 30;
        // }
        // this.getVitTimes = 5;
        // this.sweepTime = 5;
        // this.adShareCount = 0;
        // this.settleShareCnt = 0;
        // this.chapterSweepTime = [];
        // for(let i = 0; i <= 121; i++){
        //     this.chapterSweepTime.push(5);
        // }
        // if (this.stayTime === 0) {
        //     this.stayTime = Date.now();
        // }
        // this.stayDay = 0;
        this.save();
    }


    toJson() {
        return {
            propData: this.propData,
            buildData: this.buildData,
            buildRoleData: this.buildRoleData,
            manyRoleData: this.manyRoleData,
        };
    }



    save() {
        blhkc.saveData(this.uid, this.toJson());
    }

}
