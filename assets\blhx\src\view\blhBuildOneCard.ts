import { OwnedHero } from "../items/blhCommonData";
import blhHeroMgr from "../mgr/blhHeroMgr";
import blhResMgr from "../mgr/blhResMgr";
import { blhStatic } from "../mgr/blhStatic";
import blhkc from "../utils/blhkc";

const {ccclass, property} = cc._decorator;

@ccclass
export default class blhBuildOneCard extends cc.Component {

    @property({
        displayName: "英雄名",
        type: cc.Label
    })
    lab_hero: cc.Label = null;

    start () {
    }

    init(roleId: number){
        console.error("buildOneCard: ", roleId)
        let heroData = blhStatic.heroMgr.getHeroData(roleId + 1);
        this.lab_hero.string = heroData.name;
        let heroCnt = blhStatic.commonData.buildRoleData.pieceNumList["" + roleId] || 0;
        heroCnt += 1;
        blhStatic.commonData.buildRoleData.pieceNumList["" + roleId] = heroCnt;
        let len = blhStatic.commonData.manyRoleData.length + 1;
        console.error("length: ", len);
        const hero: OwnedHero = {
            instanceId: "" + len,
            roleId: roleId,
            level: 0,
            star: blhkc.randomInt(1, 100),
            fightNum: 0,
            fightState: 0
        }
        blhStatic.commonData.manyRoleData.push(hero)
        blhStatic.commonData.save();
        
    }


    static create(parent: cc.Node, roleId: number): void {
        const node: cc.Node = cc.instantiate(blhResMgr.ins().getNode("buildOneCard"));
        node.parent = parent;
        node.getComponent(blhBuildOneCard).init(roleId);
    }

}
