import { battleMode, Prefabs, blPropType, Msg } from "../mgr/blhConst";
import blhResMgr from "../mgr/blhResMgr";
import { blhStatic } from "../mgr/blhStatic";
import blhBoatyarRoleItem from "./blhBoatyarRoleItem";

const { ccclass, property } = cc._decorator;

@ccclass
export default class blhBoatyarUI extends cc.Component {

    @property({
        displayName: "金币数量",
        type: cc.Label
    })
    lab_goods: cc.Label = null;

    @property({
        displayName: "钻石数量",
        type: cc.Label
    })
    lab_diamond: cc.Label = null;

    @property({
        displayName: "容器节点",
        type: cc.Node
    })
    node_content: cc.Node = null;

    @property({
        displayName: "等级按钮",
        type: cc.Node
    })
    node_level: cc.Node = null;




    protected onEnable(): void {
        // console.error("角色数据: ", blhStatic.commonData.manyRoleData);
        // const sortedHeros = blhStatic.commonData.manyRoleData.slice().sort((a, b) =>{
        //     if(b.star === a.star){
        //         return b.level - a.level;
        //     }
        //     return b.star - a.star;
        // })
        // console.error("排序后数据: ", sortedHeros);
        this.node_level.on(cc.Node.EventType.TOUCH_END, this.clickSort, this);

    }

    //点击等级筛选
    clickSort() {
        // const heros = this.levelSort();
        const sortedHeros = blhStatic.commonData.manyRoleData.slice().sort((a, b) => {
            return b.level - a.level;
        });
        console.error("等级筛选后角色数据: ", Math.round(sortedHeros.length / 4));
        this.node_content.removeAllChildren();
        let len = Math.round(sortedHeros.length / 4);
        this.node_content.height = len * 280 + (len - 1) * 10;
        sortedHeros.forEach(element => {
            blhBoatyarRoleItem.create(this.node_content, element);
        });

    }


    // //等级筛选
    // levelSort() {
    //     console.error("角色数据: ", blhStatic.commonData.manyRoleData);
    //     const sortedHeros = blhStatic.commonData.manyRoleData.slice().sort((a, b) => {
    //         if (b.star === a.star) {
    //             return b.level - a.level;
    //         }
    //         return b.star - a.star;
    //     })
    //     // console.error("排序后数据: ", sortedHeros);
    //     return sortedHeros;
    // }


    //刷新labe
    refreshLabel() {
        this.lab_goods.string = `${blhStatic.commonData.propData["" + blPropType.goods]}`;
        this.lab_diamond.string = `${blhStatic.commonData.propData["" + blPropType.diamond]}`
    }

    static create(parent: cc.Node): void {
        const node: cc.Node = cc.instantiate(blhResMgr.ins().getNode(Prefabs.boatyarUI));
        node.parent = parent;
    }


}