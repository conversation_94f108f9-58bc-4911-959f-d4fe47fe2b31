import { battleMode, Prefabs, blPropType, Msg } from "../mgr/blhConst";
import blhResMgr from "../mgr/blhResMgr";
import { blhStatic } from "../mgr/blhStatic";

const { ccclass, property } = cc._decorator;

@ccclass
export default class blhBoatyarUI extends cc.Component {

    @property({
        displayName: "金币数量",
        type: cc.Label
    })
    lab_goods: cc.Label = null;

    @property({
        displayName: "钻石数量",
        type: cc.Label
    })
    lab_diamond: cc.Label = null;


    protected onEnable(): void {

    }

    //刷新labe
    refreshLabel(){
        this.lab_goods.string = `${blhStatic.commonData.propData["" + blPropType.goods]}`;
        this.lab_diamond.string = `${blhStatic.commonData.propData["" + blPropType.diamond]}`
    }

    static create(parent: cc.Node): void {
        const node: cc.Node = cc.instantiate(blhResMgr.ins().getNode(Prefabs.boatyarUI));
        node.parent = parent;
    }


}