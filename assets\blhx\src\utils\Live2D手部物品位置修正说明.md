# Live2D 手部物品位置修正解决方案

## 问题描述
`dafeng_7` 模型在播放 `idle_motion3` 动画时，手里的物品位置会发生偏移，而 `ankeleiqi_2_hx` 模型播放相同动画时没有问题。

## 问题原因分析
1. **模型复杂度差异**：`dafeng_7` 模型包含更多手部相关参数
   - `ParamHandLAngle`, `ParamHandLChange`, `ParamHandLAnimation`
   - `ParamHandRAngle`, `ParamHandRChange`, `ParamHandRAnimation`
   - 等27个手部相关参数

2. **动画数据差异**：`dafeng_7` 的动画文件包含复杂的手部动画曲线，而 `ankeleiqi_2_hx` 没有这些参数

3. **物理引擎影响**：`dafeng_7` 模型的物理配置更复杂，可能影响手部物品的位置计算

## 解决方案

### 1. 参数修正系统
在 `Live2dComponent.ts` 中添加了模型特定的手部参数修正：

```typescript
private static MODEL_ADJUSTMENTS = {
    'dafeng_7': {
        scaleAdjust: 0.95,
        offsetX: -15,
        offsetY: 8,
        handItemCorrection: {
            'ParamHandLAngle': -0.05,      // 左手角度微调
            'ParamHandLChange': 0.1,       // 左手变化参数
            'ParamHandLAnimation': -0.02,  // 左手动画参数
            'ParamHandRAngle': -0.03,      // 右手角度微调
            'ParamHandRChange': 0.08,      // 右手变化参数
            'ParamHandRAnimation': -0.015  // 右手动画参数
        }
    }
};
```

### 2. 实时参数修正
在 `LAppModel.ts` 的 `update()` 方法中添加了 `applyHandItemCorrection()` 调用，在每帧更新时应用手部参数修正。

### 3. 调试工具
创建了 `blhLive2dDebugger.ts` 调试组件，支持：
- **A/B键**：调整X轴位置偏移
- **↑↓键**：调整Y轴位置偏移  
- **Q/E键**：调整缩放
- **数字键1-6**：调整手部参数
- **T键**：测试播放动画
- **R键**：重置所有参数

## 使用方法

### 自动修正（推荐）
1. 直接加载 `dafeng_7` 模型，系统会自动应用预设的修正参数
2. 播放 `idle_motion3` 动画查看效果

### 手动调试
1. 在场景中添加 `blhLive2dDebugger` 组件
2. 设置 `showDebugPanel = true`
3. 运行游戏，使用键盘调试参数
4. 调试完成后调用 `exportCurrentConfig()` 导出配置

### 代码集成
```typescript
// 在blhHallUI.ts中
this.l2d.loadModel('dafeng_7'); // 自动应用修正

// 或手动设置
this.l2d.offsetX = -15;
this.l2d.offsetY = 8;
this.l2d.modelScaleAdjust = 0.95;
```

## 参数说明

### 位置偏移参数
- `offsetX`: X轴偏移（负值向左，正值向右）
- `offsetY`: Y轴偏移（负值向下，正值向上）
- `modelScaleAdjust`: 模型缩放调整（1.0为原始大小）

### 手部修正参数
- `ParamHandLAngle`: 左手角度调整
- `ParamHandLChange`: 左手变化参数
- `ParamHandLAnimation`: 左手动画参数
- `ParamHandRAngle`: 右手角度调整
- `ParamHandRChange`: 右手变化参数
- `ParamHandRAnimation`: 右手动画参数

## 扩展性
系统支持为其他模型添加特定的修正配置，只需在 `MODEL_ADJUSTMENTS` 中添加新的模型配置即可。

## 注意事项
1. 参数修正使用较小的权重(0.3)进行平滑过渡，避免突兀的变化
2. 如果参数不存在会自动跳过，不会影响其他模型
3. 建议先使用调试工具找到合适的参数值，再更新到配置中
