import Live2dComponent from "../../../live2d/Live2dComponent";

/**
 * Live2D调试工具
 * 用于在运行时调整模型位置和缩放参数
 */
const { ccclass, property } = cc._decorator;

@ccclass
export default class blhLive2dDebugger extends cc.Component {

    @property(Live2dComponent)
    live2dComponent: Live2dComponent = null;

    @property({ tooltip: "是否显示调试面板" })
    showDebugPanel: boolean = false;

    @property({ tooltip: "X轴偏移调整步长" })
    offsetXStep: number = 1;

    @property({ tooltip: "Y轴偏移调整步长" })
    offsetYStep: number = 1;

    @property({ tooltip: "缩放调整步长" })
    scaleStep: number = 0.01;

    private debugPanel: cc.Node = null;

    onLoad() {
        if (this.showDebugPanel) {
            this.createDebugPanel();
        }
        
        // 添加键盘监听
        cc.systemEvent.on(cc.SystemEvent.EventType.KEY_DOWN, this.onKeyDown, this);
    }

    onDestroy() {
        cc.systemEvent.off(cc.SystemEvent.EventType.KEY_DOWN, this.onKeyDown, this);
    }

    private onKeyDown(event: cc.Event.EventKeyboard) {
        if (!this.live2dComponent) return;

        switch (event.keyCode) {
            case cc.macro.KEY.left:
                this.adjustOffset(-this.offsetXStep, 0);
                break;
            case cc.macro.KEY.right:
                this.adjustOffset(this.offsetXStep, 0);
                break;
            case cc.macro.KEY.up:
                this.adjustOffset(0, this.offsetYStep);
                break;
            case cc.macro.KEY.down:
                this.adjustOffset(0, -this.offsetYStep);
                break;
            case cc.macro.KEY.q:
                this.adjustScale(-this.scaleStep);
                break;
            case cc.macro.KEY.e:
                this.adjustScale(this.scaleStep);
                break;
            case cc.macro.KEY.r:
                this.resetAdjustments();
                break;
        }
    }

    private adjustOffset(deltaX: number, deltaY: number) {
        const newOffsetX = this.live2dComponent.offsetX + deltaX;
        const newOffsetY = this.live2dComponent.offsetY + deltaY;
        
        this.live2dComponent.adjustModel(newOffsetX, newOffsetY);
        
        console.log(`Live2D位置调整: offsetX=${newOffsetX}, offsetY=${newOffsetY}`);
        this.updateDebugInfo();
    }

    private adjustScale(deltaScale: number) {
        const newScale = this.live2dComponent.modelScaleAdjust + deltaScale;
        
        this.live2dComponent.adjustModel(undefined, undefined, newScale);
        
        console.log(`Live2D缩放调整: scaleAdjust=${newScale}`);
        this.updateDebugInfo();
    }

    private resetAdjustments() {
        this.live2dComponent.adjustModel(0, 0, 1.0);
        console.log('Live2D参数已重置');
        this.updateDebugInfo();
    }

    private createDebugPanel() {
        // 创建调试面板UI
        this.debugPanel = new cc.Node('Live2dDebugPanel');
        this.debugPanel.parent = this.node;
        
        // 添加背景
        const bg = this.debugPanel.addComponent(cc.Sprite);
        bg.type = cc.Sprite.Type.SLICED;
        bg.sizeMode = cc.Sprite.SizeMode.CUSTOM;
        this.debugPanel.setContentSize(300, 200);
        this.debugPanel.color = new cc.Color(0, 0, 0, 128);
        
        // 添加说明文本
        const label = new cc.Node('Instructions');
        label.parent = this.debugPanel;
        const labelComp = label.addComponent(cc.Label);
        labelComp.string = "Live2D调试控制:\n方向键: 调整位置\nQ/E: 调整缩放\nR: 重置参数";
        labelComp.fontSize = 16;
        labelComp.lineHeight = 20;
        label.setPosition(0, 50);
        
        // 添加当前参数显示
        const infoLabel = new cc.Node('Info');
        infoLabel.parent = this.debugPanel;
        const infoComp = infoLabel.addComponent(cc.Label);
        infoComp.string = "当前参数: ";
        infoComp.fontSize = 14;
        infoLabel.setPosition(0, -50);
        
        this.debugPanel.setPosition(cc.winSize.width / 2 - 150, cc.winSize.height / 2 - 100);
    }

    private updateDebugInfo() {
        if (!this.debugPanel || !this.live2dComponent) return;
        
        const infoLabel = this.debugPanel.getChildByName('Info');
        if (infoLabel) {
            const labelComp = infoLabel.getComponent(cc.Label);
            labelComp.string = `当前参数:\noffsetX: ${this.live2dComponent.offsetX}\noffsetY: ${this.live2dComponent.offsetY}\nscale: ${this.live2dComponent.modelScaleAdjust.toFixed(3)}`;
        }
    }

    /**
     * 输出当前配置，用于复制到代码中
     */
    public exportCurrentConfig() {
        if (!this.live2dComponent) return;
        
        const config = {
            offsetX: this.live2dComponent.offsetX,
            offsetY: this.live2dComponent.offsetY,
            scaleAdjust: this.live2dComponent.modelScaleAdjust
        };
        
        console.log('当前Live2D配置:', JSON.stringify(config, null, 2));
        console.log('可以将此配置添加到 Live2dComponent.MODEL_ADJUSTMENTS 中');
        
        return config;
    }
}
