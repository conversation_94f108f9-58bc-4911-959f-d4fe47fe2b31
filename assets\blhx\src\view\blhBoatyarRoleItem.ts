import { OwnedHero } from "../items/blhCommonData";
import { battleMode, Prefabs, blPropType, Msg } from "../mgr/blhConst";
import blhResMgr from "../mgr/blhResMgr";
import { blhStatic } from "../mgr/blhStatic";

const { ccclass, property } = cc._decorator;

@ccclass
export default class blhBoatyarRoleItem extends cc.Component {

    @property({
        displayName: "角色名称",
        type: cc.Label
    })
    lab_roleName: cc.Label = null;
    @property({
        displayName: "角色等级",
        type: cc.Label
    })
    lab_roleLv: cc.Label = null;
    @property({
        displayName: "角色战力",
        type: cc.Label
    })
    lab_roleFight: cc.Label = null;

    @property({
        displayName: "角色星级组",
        type: [cc.Node]
    })
    nodes_stars: cc.Node[] = [];


    //初始数据
    init(data: OwnedHero){
        console.error("角色数据: ", data);
        let heroData = blhStatic.heroMgr.getHeroData(data.roleId);
        this.lab_roleName.string = heroData.name;
        this.lab_roleLv.string = "LV." + data.level;
        this.lab_roleFight.string = "战力:" + data.atk;
        this.nodes_stars.forEach((element, index) => {
            if(index < data.star){
                element.active = true;
            }else{
                element.active = false;
            }
        });
    }


    static create(parent: cc.Node, data: OwnedHero): void {
        const node: cc.Node = cc.instantiate(blhResMgr.ins().getNode("boatyarRoleItem"));
        node.parent = parent;
        node.getComponent(blhBoatyarRoleItem).init(data);
    }


}